'use client';

import React, { useState, useMemo } from 'react';
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from 'react-hook-form';
import { useController, useFormContext } from 'react-hook-form';
import { tv } from 'tailwind-variants';
import { FiEye, FiEyeOff } from 'react-icons/fi';

const inputTv = tv({
  slots: {
    container: 'relative',
    label:
      'font-aeonik-regular text-base/100 font-medium text-fg-muted-light dark:text-fg-muted-dark',
    input:
      'h-[52px] rounded-md border border-border-subtle-light bg-transparent p-3 font-aeonik-regular text-base/100 font-medium shadow-none dark:border-border-subtle-dark dark:text-white',
    iconContainer:
      'absolute left-3 top-3.5 text-accent-moderate dark:text-accent-moderate',
  },

  variants: {
    focused: {
      true: {
        input:
          'border border-accent-moderate pb-2 pt-5 dark:border-accent-moderate',
        iconContainer: '',
      },
    },
    filled: {
      true: {
        input: 'pb-2 pt-5',
        iconContainer: '',
      },
    },
    error: {
      true: {
        input: 'border-2 border-red-60 dark:border-red-80',
        label: 'dark:text-danger-80 text-red-80',
        iconContainer: '',
      },
    },
    disabled: {
      true: {
        input: 'inherit',
        iconContainer: '',
      },
    },
  },
  defaultVariants: {
    focused: false,
    filled: false,
    error: false,
    disabled: false,
  },
});

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  label?: string;
  disabled?: boolean;
  error?: string;
  isPassword?: boolean;
  icon?: any;
  iconClassName?: string;
  containerClassName?: string;
  inputClassName?: string;
  focusedInputClassName?: string;
  handleFieldBlur?: () => void;
  handleFieldUnBlur?: () => void;
  hideErrorMessage?: boolean;
  multiline?: boolean;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      'disabled' | 'valueAsNumber' | 'valueAsDate' | 'setValueAs'
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export type InputControllerType<T extends FieldValues> = {
  name: Path<T>;
  control: Control<T>;
  rules?: RegisterOptions<T, Path<T>>;
};

interface ControlledInputProps<T extends FieldValues>
  extends Omit<InputProps, 'name'>,
    InputControllerType<T> {}

// Simplified Input
export const Input = React.forwardRef<
  HTMLInputElement | HTMLTextAreaElement,
  InputProps
>(
  (
    {
      label,
      error,
      isPassword,
      icon,
      hideErrorMessage,
      iconClassName = '',
      containerClassName = '',
      inputClassName = '',
      focusedInputClassName = '',
      value,
      onBlur,
      handleFieldBlur,
      handleFieldUnBlur,
      multiline,
      ...props
    },
    ref
  ) => {
    const [showPassword, setShowPassword] = useState(!isPassword);

    const [isFocused, setIsFocused] = useState(false);
    const [isFilled, setIsFilled] = useState(!!value);

    const handleValueChange = (e: any) => {
      setIsFilled(!!e.target.value);
      props.onChange?.(e);
    };

    // Update isFocused on focus/blur
    const handleFocus = () => {
      setIsFocused(true);
      props.onFocus?.();
    };

    const handleBlur = () => {
      setIsFocused(false);
      props.onBlur?.();
    };

    const styles = useMemo(
      () =>
        inputTv({
          focused: isFocused,
          filled: isFilled,
          error: !!error,
          disabled: props.disabled,
        }),
      [isFocused, isFilled, error, props.disabled]
    );

    const Element = multiline ? 'textarea' : 'input';

    return (
      <div className={styles.container()}>
        <div className="relative">
          {label && (
            <label htmlFor={props.id} className={styles.label()}>
              {label}
            </label>
          )}
          {icon && <div className={styles.iconContainer()}>{icon}</div>}
          <Element
            {...props}
            ref={ref as any}
            id={props.id}
            type={
              isPassword
                ? showPassword
                  ? 'text'
                  : 'password'
                : (props.type ?? 'text')
            }
            className={styles.input()}
            placeholder=""
            onChange={handleValueChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
          {isPassword && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-3.5 text-gray-500 dark:text-gray-400"
            >
              {showPassword ? <FiEye /> : <FiEyeOff />}
            </button>
          )}
        </div>
        {error && !hideErrorMessage && (
          <p className="text-red-500 dark:text-red-600 text-sm mt-1">{error}</p>
        )}
      </div>
    );
  }
);

export const ControlledInput = <T extends FieldValues>(
  props: ControlledInputProps<T>
) => {
  const { control: contextControl } = useFormContext<T>();
  const {
    name,
    control = contextControl,
    rules,
    onChange: propsOnChange,
    ...rest
  } = props;

  const { field, fieldState } = useController({ name, control, rules });

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    field.onChange(e);
    if (propsOnChange) propsOnChange(e as any);
  };

  return (
    <Input
      {...(rest as React.InputHTMLAttributes<HTMLInputElement>)}
      ref={field.ref}
      value={field.value ?? ''}
      onChange={handleChange}
      onBlur={field.onBlur}
      error={props.hideErrorMessage ? undefined : fieldState.error?.message}
    />
  );
};
